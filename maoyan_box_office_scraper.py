import requests
import json
import time
import os
import random
import math

# --- 在这里添加时间判断逻辑 ---
current_hour = time.localtime().tm_hour
# 小时的范围是 0-23。我们只在 10点 到 22点 之间运行。
if not (10 <= current_hour <= 22):
    print(f"当前时间 {current_hour}点，不在允许的执行时间内（10:00-22:59），脚本退出。")
    exit() # 直接退出程序
# -----------------------------

def get_box_office_data():
    """
    从猫眼票房API获取数据
    """
    # 基础URL
    base_url = 'https://piaofang.maoyan.com/getBoxList'
    
    # 生成当前时间戳和随机索引
    timestamp = str(math.ceil(time.time() * 1000))
    index = str(round(random.random() * 1000))
    
    # 这里的signKey是固定的，实际上可能需要根据其他参数动态生成
    # 但为了简化，我们先使用固定值测试
    sign_key = '7f498fbe'
    
    # 构建请求参数
    params = {
        'date': '1',
        'isSplit': 'true',
        'timeStamp': timestamp,
        'index': index,
        'channelId': '40009',
        'sVersion': '1',
        'signKey': sign_key
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
        'Referer': 'https://piaofang.maoyan.com/dashboard',
        'Accept': 'application/json, text/plain, */*'
    }

    try:
        response = requests.get(base_url, headers=headers, params=params, timeout=10)
        response.raise_for_status()
        
        # 解析JSON响应
        data = response.json()
        return data
            
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"解析JSON失败: {e}")
        print(f"响应内容: {response.text[:200]}...")  # 打印前200个字符以便调试
        return None

def process_and_format_data(data):
    """
    处理并格式化票房数据
    """
    if not data or 'boxOffice' not in data or 'data' not in data['boxOffice'] or 'list' not in data['boxOffice']['data']:
        print("未找到有效的数据或数据格式不正确。")
        return None

    movies = data['boxOffice']['data']['list']
    if not movies:
        print("电影列表为空")
        return None
    
    # 定义一个安全的转换函数，用于处理票房值
    def parse_box_office(box_value):
        if not box_value or box_value == 'N/A':
            return 0.0
        
        clean_value = str(box_value).replace(',', '')
        
        if '万' in clean_value:
            try:
                return float(clean_value.replace('万', ''))
            except ValueError:
                return 0.0
        elif '亿' in clean_value:
            try:
                return float(clean_value.replace('亿', '')) * 10000
            except ValueError:
                return 0.0
        else:
            try:
                return float(clean_value)  # 假设无单位的票房数值是"万"
            except ValueError:
                return 0.0

    # 计算总票房
    total_box_office = sum(parse_box_office(movie.get('boxDesc', '0')) for movie in movies)
    
    # 格式化总票房，统一单位为"万"
    formatted_total = f"{total_box_office:.2f}万"
    
    # 获取当前时间
    current_time = time.strftime("%Y-%m-%d %H:%M:%S")
    
    # 格式化输出头部
    formatted_output = f"【今日实时票房同步】\n实时大盘约：{formatted_total}\n---\n"
    
    # 定义一个安全的转换函数，用于处理百分比
    def safe_float_convert(value):
        if not value:
            return 0
        clean_value = value.replace('%', '').replace('<', '')
        try:
            return float(clean_value)
        except ValueError:
            return 0
    
    # 按照票房占比排序电影列表
    sorted_movies = sorted(movies, key=lambda x: safe_float_convert(x.get('boxRate', '0')), reverse=True)
    
    # 中文数字映射
    chinese_numbers = ['①', '②', '③', '④', '⑤', '⑥', '⑦', '⑧', '⑨', '⑩']
    
    # 生成每部电影的格式化字符串
    for i, movie in enumerate(sorted_movies[:10]):
        if i < len(chinese_numbers):
            number = chinese_numbers[i]
        else:
            number = f"{i+1}."
            
        movie_name = movie.get('movieInfo', {}).get('movieName', 'N/A')
        box_office_str = movie.get('boxDesc', 'N/A')
        
        # 确保票房有"万"单位
        if box_office_str and '万' not in box_office_str and '亿' not in box_office_str:
            # 检查是否为纯数字或者可转换为数字的字符串
            try:
                float(box_office_str)
                box_office_str = f"{box_office_str}万"
            except (ValueError, TypeError):
                pass # 保持原样

        # 使用票房占比代替排片率
        box_rate = movie.get('boxRate', 'N/A')
        
        formatted_output += f"{number}{movie_name}({box_office_str})-{box_rate}\n"
    
    # 添加结尾时间信息
    formatted_output += f"---\n同步时间：{current_time}"
    
    return formatted_output

def send_to_wecom_bot(content, webhook_url):
    """
    将内容以Markdown格式发送到企业微信机器人
    
    参数说明:
    content (str): 要发送的内容，支持Markdown格式
    webhook_url (str): 企业微信机器人的Webhook URL，包含访问密钥
    
    企业微信机器人API说明:
    - 支持多种消息类型，此函数使用markdown类型
    - 发送频率限制：默认每个机器人每分钟最多发送20条消息
    - 单条消息内容不超过4096字节
    - 详细API文档: https://developer.work.weixin.qq.com/document/path/91770
    """
    if not webhook_url:
        print("未提供企业微信机器人Webhook地址，跳过发送。")
        return

    # 设置请求头，指定内容类型为JSON
    headers = {'Content-Type': 'application/json'}
    
    # 构建请求体数据
    # 注意：msgtype 必须是 "markdown"，这决定了消息的渲染方式
    data = {
        "msgtype": "markdown",  # 消息类型：markdown格式
        "markdown": {
            "content": content  # 消息内容，支持markdown语法
        }
    }
    
    try:
        # 发送POST请求到企业微信机器人API
        response = requests.post(webhook_url, headers=headers, data=json.dumps(data), timeout=10)
        response.raise_for_status()  # 如果状态码不是200，抛出异常
        
        # 解析响应结果
        result = response.json()
        
        # 企业微信API的标准响应格式包含errcode字段
        # errcode为0表示成功，其他值表示失败
        if result.get('errcode') == 0:
            print("成功发送到企业微信机器人。")
        else:
            print(f"发送到企业微信机器人失败: {result.get('errmsg')}")
    except requests.exceptions.RequestException as e:
        print(f"请求企业微信机器人失败: {e}")

if __name__ == '__main__':
    # 企业微信机器人Webhook地址
    # 格式说明: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=你的机器人密钥
    # 此密钥在企业微信管理后台创建机器人时生成，用于鉴权
    # 注意：密钥应当保密，不应在生产环境中硬编码
    WECOM_WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=09de6958-992c-453b-93ec-1c48d0c1a1fe"

    print("开始爬取猫眼电影票房数据...")
    raw_data = get_box_office_data()
    
    if raw_data:
        # 格式化并显示数据
        formatted_data = process_and_format_data(raw_data)
        if formatted_data:
            print("\n" + formatted_data + "\n")

            # 在消息末尾添加Markdown格式的可点击链接
            # <font color="info"> 使用企业微信支持的文本颜色，显示为蓝色
            # Markdown链接格式: [链接文本](URL地址)
            report_content = formatted_data + '\n\n<font color="info">[点击查看实时票房看板](https://piaofang.maoyan.com)</font>'
            
            # 发送到企业微信机器人
            # 参数1: 处理好的消息内容（带有Markdown格式）
            # 参数2: 企业微信机器人的Webhook URL
            send_to_wecom_bot(report_content, WECOM_WEBHOOK_URL)
        else:
            print("格式化数据失败。")
    else:
        print("获取数据失败，请检查网络连接。")
    
    print("所有任务完成。") 