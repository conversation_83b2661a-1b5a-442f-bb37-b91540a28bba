import requests
import json
import time
import os
import random
import math

# --- 在这里添加时间判断逻辑 ---
current_hour = time.localtime().tm_hour
# 小时的范围是 0-23。我们只在 10点 到 22点 之间运行。
if not (10 <= current_hour <= 22):
    print(f"当前时间 {current_hour}点，不在允许的执行时间内（10:00-22:59），脚本退出。")
    exit() # 直接退出程序
# -----------------------------

def get_box_office_data():
    """
    从猫眼票房API获取数据
    """
    # 基础URL
    base_url = 'https://piaofang.maoyan.com/getBoxList'
    
    # 生成当前时间戳和随机索引
    timestamp = str(math.ceil(time.time() * 1000))
    index = str(round(random.random() * 1000))
    
    # 这里的signKey是固定的，实际上可能需要根据其他参数动态生成
    # 但为了简化，我们先使用固定值测试
    sign_key = '7f498fbe'
    
    # 构建请求参数
    params = {
        'date': '1',
        'isSplit': 'true',
        'timeStamp': timestamp,
        'index': index,
        'channelId': '40009',
        'sVersion': '1',
        'signKey': sign_key
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
        'Referer': 'https://piaofang.maoyan.com/dashboard',
        'Accept': 'application/json, text/plain, */*'
    }

    try:
        response = requests.get(base_url, headers=headers, params=params, timeout=10)
        response.raise_for_status()
        
        # 解析JSON响应
        data = response.json()
        return data
            
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"解析JSON失败: {e}")
        print(f"响应内容: {response.text[:200]}...")  # 打印前200个字符以便调试
        return None

def process_and_format_data(data):
    """
    处理并格式化票房数据
    """
    if not data or 'boxOffice' not in data or 'data' not in data['boxOffice'] or 'list' not in data['boxOffice']['data']:
        print("未找到有效的数据或数据格式不正确。")
        return None

    movies = data['boxOffice']['data']['list']
    if not movies:
        print("电影列表为空")
        return None
    
    # 定义一个安全的转换函数，用于处理票房值
    def parse_box_office(box_value):
        if not box_value or box_value == 'N/A':
            return 0.0
        
        clean_value = str(box_value).replace(',', '')
        
        if '万' in clean_value:
            try:
                return float(clean_value.replace('万', ''))
            except ValueError:
                return 0.0
        elif '亿' in clean_value:
            try:
                return float(clean_value.replace('亿', '')) * 10000
            except ValueError:
                return 0.0
        else:
            try:
                return float(clean_value)  # 假设无单位的票房数值是"万"
            except ValueError:
                return 0.0

    # 计算总票房
    total_box_office = sum(parse_box_office(movie.get('boxDesc', '0')) for movie in movies)
    
    # 格式化总票房，统一单位为"万"
    formatted_total = f"{total_box_office:.2f}万"
    
    # 获取当前时间
    current_time = time.strftime("%Y-%m-%d %H:%M:%S")
    
    # 格式化输出头部
    formatted_output = f"【今日实时票房同步】\n实时大盘约：{formatted_total}\n---\n"
    
    # 定义一个安全的转换函数，用于处理百分比
    def safe_float_convert(value):
        if not value:
            return 0
        clean_value = value.replace('%', '').replace('<', '')
        try:
            return float(clean_value)
        except ValueError:
            return 0
    
    # 按照票房占比排序电影列表
    sorted_movies = sorted(movies, key=lambda x: safe_float_convert(x.get('boxRate', '0')), reverse=True)
    
    # 中文数字映射
    chinese_numbers = ['①', '②', '③', '④', '⑤', '⑥', '⑦', '⑧', '⑨', '⑩']
    
    # 生成每部电影的格式化字符串
    for i, movie in enumerate(sorted_movies[:10]):
        if i < len(chinese_numbers):
            number = chinese_numbers[i]
        else:
            number = f"{i+1}."
            
        movie_name = movie.get('movieInfo', {}).get('movieName', 'N/A')
        box_office_str = movie.get('boxDesc', 'N/A')
        
        # 确保票房有"万"单位
        if box_office_str and '万' not in box_office_str and '亿' not in box_office_str:
            # 检查是否为纯数字或者可转换为数字的字符串
            try:
                float(box_office_str)
                box_office_str = f"{box_office_str}万"
            except (ValueError, TypeError):
                pass # 保持原样

        # 使用票房占比代替排片率
        box_rate = movie.get('boxRate', 'N/A')
        
        formatted_output += f"{number}{movie_name}({box_office_str})-{box_rate}\n"
    
    # 添加结尾时间信息
    formatted_output += f"---\n同步时间：{current_time}"
    
    return formatted_output

# ==================== 机器人推送系统 ====================

class BotNotifier:
    """
    机器人推送基类，定义统一的推送接口
    """
    def __init__(self, webhook_url, bot_name):
        self.webhook_url = webhook_url
        self.bot_name = bot_name

    def send_message(self, content):
        """
        发送消息的抽象方法，子类需要实现具体的发送逻辑

        参数:
        content (str): 要发送的内容

        返回:
        bool: 发送是否成功
        """
        raise NotImplementedError("子类必须实现 send_message 方法")

    def _make_request(self, data, headers=None):
        """
        通用的HTTP请求方法

        参数:
        data (dict): 请求体数据
        headers (dict): 请求头，默认为JSON格式

        返回:
        tuple: (是否成功, 响应数据)
        """
        if not self.webhook_url:
            print(f"未提供{self.bot_name}机器人Webhook地址，跳过发送。")
            return False, None

        if headers is None:
            headers = {'Content-Type': 'application/json'}

        try:
            response = requests.post(
                self.webhook_url,
                headers=headers,
                data=json.dumps(data),
                timeout=10
            )
            response.raise_for_status()
            result = response.json()
            return True, result
        except requests.exceptions.RequestException as e:
            print(f"请求{self.bot_name}机器人失败: {e}")
            return False, None
        except json.JSONDecodeError as e:
            print(f"解析{self.bot_name}机器人响应失败: {e}")
            return False, None


class WeComBotNotifier(BotNotifier):
    """
    企业微信机器人推送器

    企业微信机器人API说明:
    - 支持多种消息类型，此类使用markdown类型
    - 发送频率限制：默认每个机器人每分钟最多发送20条消息
    - 单条消息内容不超过4096字节
    - 详细API文档: https://developer.work.weixin.qq.com/document/path/91770
    """
    def __init__(self, webhook_url):
        super().__init__(webhook_url, "企业微信")

    def send_message(self, content):
        """
        发送Markdown格式消息到企业微信机器人

        参数:
        content (str): 要发送的内容，支持Markdown格式

        返回:
        bool: 发送是否成功
        """
        data = {
            "msgtype": "markdown",
            "markdown": {
                "content": content
            }
        }

        success, result = self._make_request(data)
        if success and result:
            # 企业微信API的标准响应格式包含errcode字段
            # errcode为0表示成功，其他值表示失败
            if result.get('errcode') == 0:
                print("成功发送到企业微信机器人。")
                return True
            else:
                print(f"发送到企业微信机器人失败: {result.get('errmsg')}")
                return False
        return False


class DingTalkBotNotifier(BotNotifier):
    """
    钉钉机器人推送器

    钉钉机器人API说明:
    - 支持多种消息类型，此类使用markdown类型
    - 发送频率限制：每个机器人每分钟最多发送20条消息
    - 单条消息内容不超过20000字节
    - 详细API文档: https://open.dingtalk.com/document/robots/custom-robot-access
    """
    def __init__(self, webhook_url):
        super().__init__(webhook_url, "钉钉")

    def send_message(self, content):
        """
        发送Markdown格式消息到钉钉机器人

        参数:
        content (str): 要发送的内容，支持Markdown格式

        返回:
        bool: 发送是否成功
        """
        data = {
            "msgtype": "markdown",
            "markdown": {
                "title": "猫眼票房数据同步",
                "text": content
            }
        }

        success, result = self._make_request(data)
        if success and result:
            # 钉钉API的标准响应格式包含errcode字段
            # errcode为0表示成功，其他值表示失败
            if result.get('errcode') == 0:
                print("成功发送到钉钉机器人。")
                return True
            else:
                print(f"发送到钉钉机器人失败: {result.get('errmsg')}")
                return False
        return False


class BotNotificationManager:
    """
    机器人推送管理器，支持同时向多个机器人推送消息
    """
    def __init__(self):
        self.notifiers = []

    def add_notifier(self, notifier):
        """
        添加一个机器人推送器

        参数:
        notifier (BotNotifier): 机器人推送器实例
        """
        if isinstance(notifier, BotNotifier):
            self.notifiers.append(notifier)
        else:
            raise TypeError("notifier 必须是 BotNotifier 的实例")

    def send_to_all(self, content):
        """
        向所有已配置的机器人发送消息

        参数:
        content (str): 要发送的内容

        返回:
        dict: 各个机器人的发送结果 {bot_name: success}
        """
        results = {}
        for notifier in self.notifiers:
            try:
                success = notifier.send_message(content)
                results[notifier.bot_name] = success
            except Exception as e:
                print(f"发送到{notifier.bot_name}机器人时发生异常: {e}")
                results[notifier.bot_name] = False

        return results

    def get_notifier_count(self):
        """
        获取已配置的机器人推送器数量

        返回:
        int: 推送器数量
        """
        return len(self.notifiers)


# ==================== 兼容性函数 ====================

def send_to_wecom_bot(content, webhook_url):
    """
    兼容性函数：保持原有的企业微信推送接口不变

    参数说明:
    content (str): 要发送的内容，支持Markdown格式
    webhook_url (str): 企业微信机器人的Webhook URL，包含访问密钥
    """
    notifier = WeComBotNotifier(webhook_url)
    return notifier.send_message(content)

if __name__ == '__main__':
    # ==================== 机器人配置区域 ====================
    # 企业微信机器人Webhook地址
    # 格式说明: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=你的机器人密钥
    # 此密钥在企业微信管理后台创建机器人时生成，用于鉴权
    WECOM_WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=09de6958-992c-453b-93ec-1c48d0c1a1fe"

    # 钉钉机器人Webhook地址（可选）
    # 格式说明: https://oapi.dingtalk.com/robot/send?access_token=你的机器人密钥
    # 此密钥在钉钉管理后台创建机器人时生成，用于鉴权
    DINGTALK_WEBHOOK_URL = ""  # 如需启用钉钉推送，请填入正确的Webhook地址

    # 注意：所有密钥应当保密，不应在生产环境中硬编码
    # 建议使用环境变量或配置文件来管理这些敏感信息
    # =====================================================

    print("开始爬取猫眼电影票房数据...")
    raw_data = get_box_office_data()

    if raw_data:
        # 格式化并显示数据
        formatted_data = process_and_format_data(raw_data)
        if formatted_data:
            print("\n" + formatted_data + "\n")

            # 在消息末尾添加Markdown格式的可点击链接
            # <font color="info"> 使用企业微信支持的文本颜色，显示为蓝色
            # Markdown链接格式: [链接文本](URL地址)
            report_content = formatted_data + '\n\n<font color="info">[点击查看实时票房看板](https://piaofang.maoyan.com)</font>'

            # ==================== 推送消息到机器人 ====================
            # 创建推送管理器
            notification_manager = BotNotificationManager()

            # 添加企业微信机器人（如果配置了URL）
            if WECOM_WEBHOOK_URL:
                wecom_notifier = WeComBotNotifier(WECOM_WEBHOOK_URL)
                notification_manager.add_notifier(wecom_notifier)

            # 添加钉钉机器人（如果配置了URL）
            if DINGTALK_WEBHOOK_URL:
                dingtalk_notifier = DingTalkBotNotifier(DINGTALK_WEBHOOK_URL)
                notification_manager.add_notifier(dingtalk_notifier)

            # 发送消息到所有配置的机器人
            if notification_manager.get_notifier_count() > 0:
                print(f"准备向 {notification_manager.get_notifier_count()} 个机器人推送消息...")
                results = notification_manager.send_to_all(report_content)

                # 输出推送结果摘要
                success_count = sum(1 for success in results.values() if success)
                total_count = len(results)
                print(f"推送完成：{success_count}/{total_count} 个机器人推送成功")

                # 详细结果（可选，用于调试）
                for bot_name, success in results.items():
                    status = "成功" if success else "失败"
                    print(f"  - {bot_name}: {status}")
            else:
                print("未配置任何机器人推送，跳过推送步骤。")
                print("提示：请在配置区域填入正确的Webhook地址以启用推送功能。")
            # =====================================================
        else:
            print("格式化数据失败。")
    else:
        print("获取数据失败，请检查网络连接。")

    print("所有任务完成。")