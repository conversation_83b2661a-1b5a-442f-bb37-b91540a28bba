# -*- coding: utf-8 -*-
"""
机器人推送配置文件示例

使用说明：
1. 复制此文件为 bot_config.py
2. 填入您的实际机器人Webhook地址
3. 在主程序中导入配置：from bot_config import BOT_CONFIGS
4. 根据需要启用或禁用特定的机器人推送

注意事项：
- 请勿将包含真实密钥的配置文件提交到版本控制系统
- 建议将 bot_config.py 添加到 .gitignore 文件中
- 生产环境建议使用环境变量管理敏感信息
"""

# ==================== 机器人配置 ====================

BOT_CONFIGS = {
    # 企业微信机器人配置
    "wecom": {
        "enabled": True,  # 是否启用此机器人
        "webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_WECOM_KEY_HERE",
        "description": "企业微信群机器人 - 主要推送渠道"
    },
    
    # 钉钉机器人配置
    "dingtalk": {
        "enabled": False,  # 是否启用此机器人
        "webhook_url": "https://oapi.dingtalk.com/robot/send?access_token=YOUR_DINGTALK_TOKEN_HERE",
        "description": "钉钉群机器人 - 备用推送渠道"
    },
    
    # 飞书机器人配置（预留，未来可扩展）
    "feishu": {
        "enabled": False,  # 是否启用此机器人
        "webhook_url": "https://open.feishu.cn/open-apis/bot/v2/hook/YOUR_FEISHU_TOKEN_HERE",
        "description": "飞书群机器人 - 预留扩展"
    },
    
    # Slack机器人配置（预留，未来可扩展）
    "slack": {
        "enabled": False,  # 是否启用此机器人
        "webhook_url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK",
        "description": "Slack频道机器人 - 国际化支持"
    }
}

# ==================== 推送设置 ====================

PUSH_SETTINGS = {
    # 是否在控制台显示详细的推送结果
    "show_detailed_results": True,
    
    # 推送失败时是否重试
    "retry_on_failure": False,
    
    # 重试次数（如果启用重试）
    "max_retries": 2,
    
    # 重试间隔（秒）
    "retry_interval": 5,
    
    # 推送超时时间（秒）
    "timeout": 10
}

# ==================== 消息格式设置 ====================

MESSAGE_SETTINGS = {
    # 是否在消息末尾添加时间戳
    "add_timestamp": True,
    
    # 是否添加查看链接
    "add_view_link": True,
    
    # 自定义链接文本
    "link_text": "点击查看实时票房看板",
    
    # 链接地址
    "link_url": "https://piaofang.maoyan.com",
    
    # 消息标题（用于支持标题的机器人，如钉钉）
    "message_title": "猫眼票房数据同步"
}

# ==================== 辅助函数 ====================

def get_enabled_bots():
    """
    获取所有启用的机器人配置
    
    返回:
    dict: 启用的机器人配置字典
    """
    return {name: config for name, config in BOT_CONFIGS.items() if config.get("enabled", False)}

def get_bot_webhook(bot_name):
    """
    获取指定机器人的Webhook地址
    
    参数:
    bot_name (str): 机器人名称
    
    返回:
    str: Webhook地址，如果机器人未启用或不存在则返回None
    """
    bot_config = BOT_CONFIGS.get(bot_name)
    if bot_config and bot_config.get("enabled", False):
        return bot_config.get("webhook_url")
    return None

def validate_config():
    """
    验证配置的有效性
    
    返回:
    tuple: (是否有效, 错误信息列表)
    """
    errors = []
    enabled_bots = get_enabled_bots()
    
    if not enabled_bots:
        errors.append("警告：未启用任何机器人推送")
    
    for name, config in enabled_bots.items():
        webhook_url = config.get("webhook_url", "")
        if not webhook_url or "YOUR_" in webhook_url:
            errors.append(f"错误：{name} 机器人的Webhook地址未正确配置")
    
    return len(errors) == 0, errors

# ==================== 使用示例 ====================

if __name__ == "__main__":
    # 验证配置
    is_valid, errors = validate_config()
    
    print("=== 机器人配置验证 ===")
    if is_valid:
        print("✅ 配置验证通过")
    else:
        print("❌ 配置验证失败：")
        for error in errors:
            print(f"  - {error}")
    
    print("\n=== 启用的机器人 ===")
    enabled_bots = get_enabled_bots()
    if enabled_bots:
        for name, config in enabled_bots.items():
            print(f"✅ {name}: {config.get('description', '无描述')}")
    else:
        print("❌ 未启用任何机器人")
    
    print("\n=== 推送设置 ===")
    for key, value in PUSH_SETTINGS.items():
        print(f"  {key}: {value}")
