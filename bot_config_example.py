# -*- coding: utf-8 -*-
"""
机器人推送配置文件示例

使用说明：
1. 复制此文件为 bot_config.py
2. 填入您的实际机器人Webhook地址
3. 在主程序中导入配置：from bot_config import BOT_CONFIGS
4. 根据需要启用或禁用特定的机器人推送

注意事项：
- 请勿将包含真实密钥的配置文件提交到版本控制系统
- 建议将 bot_config.py 添加到 .gitignore 文件中
- 生产环境建议使用环境变量管理敏感信息
"""

# ==================== 机器人配置 ====================

BOT_CONFIGS = {
    # 企业微信机器人配置（支持多个群）
    "wecom_groups": [
        {
            "name": "技术团队群",
            "enabled": True,
            "webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_TECH_TEAM_KEY_HERE",
            "description": "技术团队日常沟通群"
        },
        {
            "name": "运营团队群",
            "enabled": True,
            "webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_OPERATION_TEAM_KEY_HERE",
            "description": "运营团队数据同步群"
        },
        {
            "name": "管理层群",
            "enabled": False,  # 可以选择性启用
            "webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_MANAGEMENT_KEY_HERE",
            "description": "管理层决策参考群"
        }
    ],

    # 钉钉机器人配置（支持多个群）
    "dingtalk_groups": [
        {
            "name": "产品团队群",
            "enabled": False,
            "webhook_url": "https://oapi.dingtalk.com/robot/send?access_token=YOUR_PRODUCT_TEAM_TOKEN_HERE",
            "description": "产品团队协作群"
        },
        {
            "name": "市场团队群",
            "enabled": False,
            "webhook_url": "https://oapi.dingtalk.com/robot/send?access_token=YOUR_MARKETING_TEAM_TOKEN_HERE",
            "description": "市场团队数据分析群"
        },
        {
            "name": "客服团队群",
            "enabled": False,
            "webhook_url": "https://oapi.dingtalk.com/robot/send?access_token=YOUR_CUSTOMER_SERVICE_TOKEN_HERE",
            "description": "客服团队信息同步群"
        }
    ],

    # 飞书机器人配置（预留，未来可扩展）
    "feishu_groups": [
        {
            "name": "研发团队群",
            "enabled": False,
            "webhook_url": "https://open.feishu.cn/open-apis/bot/v2/hook/YOUR_DEV_TEAM_FEISHU_TOKEN_HERE",
            "description": "研发团队飞书群"
        }
    ],

    # Slack机器人配置（预留，未来可扩展）
    "slack_channels": [
        {
            "name": "general",
            "enabled": False,
            "webhook_url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK",
            "description": "Slack通用频道"
        }
    ]
}

# ==================== 推送设置 ====================

PUSH_SETTINGS = {
    # 是否在控制台显示详细的推送结果
    "show_detailed_results": True,
    
    # 推送失败时是否重试
    "retry_on_failure": False,
    
    # 重试次数（如果启用重试）
    "max_retries": 2,
    
    # 重试间隔（秒）
    "retry_interval": 5,
    
    # 推送超时时间（秒）
    "timeout": 10
}

# ==================== 消息格式设置 ====================

MESSAGE_SETTINGS = {
    # 是否在消息末尾添加时间戳
    "add_timestamp": True,
    
    # 是否添加查看链接
    "add_view_link": True,
    
    # 自定义链接文本
    "link_text": "点击查看实时票房看板",
    
    # 链接地址
    "link_url": "https://piaofang.maoyan.com",
    
    # 消息标题（用于支持标题的机器人，如钉钉）
    "message_title": "猫眼票房数据同步"
}

# ==================== 辅助函数 ====================

def get_enabled_bots():
    """
    获取所有启用的机器人配置

    返回:
    list: 启用的机器人配置列表，每个元素包含 type, name, webhook_url, description
    """
    enabled_bots = []

    # 处理企业微信群
    for wecom_config in BOT_CONFIGS.get("wecom_groups", []):
        if wecom_config.get("enabled", False):
            enabled_bots.append({
                "type": "wecom",
                "name": wecom_config.get("name", "未命名企业微信群"),
                "webhook_url": wecom_config.get("webhook_url", ""),
                "description": wecom_config.get("description", "")
            })

    # 处理钉钉群
    for dingtalk_config in BOT_CONFIGS.get("dingtalk_groups", []):
        if dingtalk_config.get("enabled", False):
            enabled_bots.append({
                "type": "dingtalk",
                "name": dingtalk_config.get("name", "未命名钉钉群"),
                "webhook_url": dingtalk_config.get("webhook_url", ""),
                "description": dingtalk_config.get("description", "")
            })

    # 处理飞书群
    for feishu_config in BOT_CONFIGS.get("feishu_groups", []):
        if feishu_config.get("enabled", False):
            enabled_bots.append({
                "type": "feishu",
                "name": feishu_config.get("name", "未命名飞书群"),
                "webhook_url": feishu_config.get("webhook_url", ""),
                "description": feishu_config.get("description", "")
            })

    # 处理Slack频道
    for slack_config in BOT_CONFIGS.get("slack_channels", []):
        if slack_config.get("enabled", False):
            enabled_bots.append({
                "type": "slack",
                "name": slack_config.get("name", "未命名Slack频道"),
                "webhook_url": slack_config.get("webhook_url", ""),
                "description": slack_config.get("description", "")
            })

    return enabled_bots

def get_bot_webhooks_by_type(bot_type):
    """
    根据机器人类型获取所有启用的Webhook地址

    参数:
    bot_type (str): 机器人类型 ('wecom', 'dingtalk', 'feishu', 'slack')

    返回:
    list: 包含 name 和 webhook_url 的字典列表
    """
    enabled_bots = get_enabled_bots()
    return [
        {"name": bot["name"], "webhook_url": bot["webhook_url"]}
        for bot in enabled_bots
        if bot["type"] == bot_type and bot["webhook_url"]
    ]

def validate_config():
    """
    验证配置的有效性

    返回:
    tuple: (是否有效, 错误信息列表)
    """
    errors = []
    enabled_bots = get_enabled_bots()

    if not enabled_bots:
        errors.append("警告：未启用任何机器人推送")

    for bot in enabled_bots:
        bot_name = f"{bot['type']}-{bot['name']}"
        webhook_url = bot.get("webhook_url", "")
        if not webhook_url or "YOUR_" in webhook_url:
            errors.append(f"错误：{bot_name} 机器人的Webhook地址未正确配置")

    return len(errors) == 0, errors

# ==================== 使用示例 ====================

if __name__ == "__main__":
    # 验证配置
    is_valid, errors = validate_config()
    
    print("=== 机器人配置验证 ===")
    if is_valid:
        print("✅ 配置验证通过")
    else:
        print("❌ 配置验证失败：")
        for error in errors:
            print(f"  - {error}")
    
    print("\n=== 启用的机器人 ===")
    enabled_bots = get_enabled_bots()
    if enabled_bots:
        # 按类型分组显示
        bot_types = {}
        for bot in enabled_bots:
            bot_type = bot['type']
            if bot_type not in bot_types:
                bot_types[bot_type] = []
            bot_types[bot_type].append(bot)

        for bot_type, bots in bot_types.items():
            type_name = {
                'wecom': '企业微信',
                'dingtalk': '钉钉',
                'feishu': '飞书',
                'slack': 'Slack'
            }.get(bot_type, bot_type)

            print(f"\n📱 {type_name} ({len(bots)}个群):")
            for bot in bots:
                print(f"  ✅ {bot['name']}: {bot.get('description', '无描述')}")
    else:
        print("❌ 未启用任何机器人")
    
    print("\n=== 推送设置 ===")
    for key, value in PUSH_SETTINGS.items():
        print(f"  {key}: {value}")
