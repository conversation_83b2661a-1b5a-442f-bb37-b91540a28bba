# -*- coding: utf-8 -*-
"""
机器人推送系统测试脚本

用于验证重构后的机器人推送系统是否正常工作
"""

import requests
import json
import time

# ==================== 机器人推送系统 ====================

class BotNotifier:
    """
    机器人推送基类，定义统一的推送接口
    """
    def __init__(self, webhook_url, bot_name):
        self.webhook_url = webhook_url
        self.bot_name = bot_name
    
    def send_message(self, content):
        """
        发送消息的抽象方法，子类需要实现具体的发送逻辑
        """
        raise NotImplementedError("子类必须实现 send_message 方法")
    
    def _make_request(self, data, headers=None):
        """
        通用的HTTP请求方法
        """
        if not self.webhook_url:
            print(f"未提供{self.bot_name}机器人Webhook地址，跳过发送。")
            return False, None
        
        if headers is None:
            headers = {'Content-Type': 'application/json'}
        
        try:
            # 模拟请求（测试模式）
            print(f"[模拟] 向{self.bot_name}机器人发送请求...")
            print(f"[模拟] URL: {self.webhook_url[:50]}...")
            print(f"[模拟] 数据: {json.dumps(data, ensure_ascii=False)[:100]}...")
            
            # 在实际环境中，这里会发送真实的HTTP请求
            # response = requests.post(self.webhook_url, headers=headers, data=json.dumps(data), timeout=10)
            # response.raise_for_status()
            # result = response.json()
            
            # 模拟成功响应
            result = {"errcode": 0, "errmsg": "ok"}
            return True, result
            
        except Exception as e:
            print(f"请求{self.bot_name}机器人失败: {e}")
            return False, None


class WeComBotNotifier(BotNotifier):
    """
    企业微信机器人推送器
    """
    def __init__(self, webhook_url):
        super().__init__(webhook_url, "企业微信")
    
    def send_message(self, content):
        """
        发送Markdown格式消息到企业微信机器人
        """
        data = {
            "msgtype": "markdown",
            "markdown": {
                "content": content
            }
        }
        
        success, result = self._make_request(data)
        if success and result:
            if result.get('errcode') == 0:
                print("成功发送到企业微信机器人。")
                return True
            else:
                print(f"发送到企业微信机器人失败: {result.get('errmsg')}")
                return False
        return False


class DingTalkBotNotifier(BotNotifier):
    """
    钉钉机器人推送器
    """
    def __init__(self, webhook_url):
        super().__init__(webhook_url, "钉钉")
    
    def send_message(self, content):
        """
        发送Markdown格式消息到钉钉机器人
        """
        data = {
            "msgtype": "markdown",
            "markdown": {
                "title": "猫眼票房数据同步",
                "text": content
            }
        }
        
        success, result = self._make_request(data)
        if success and result:
            if result.get('errcode') == 0:
                print("成功发送到钉钉机器人。")
                return True
            else:
                print(f"发送到钉钉机器人失败: {result.get('errmsg')}")
                return False
        return False


class BotNotificationManager:
    """
    机器人推送管理器，支持同时向多个机器人推送消息
    """
    def __init__(self):
        self.notifiers = []
    
    def add_notifier(self, notifier):
        """
        添加一个机器人推送器
        """
        if isinstance(notifier, BotNotifier):
            self.notifiers.append(notifier)
        else:
            raise TypeError("notifier 必须是 BotNotifier 的实例")
    
    def send_to_all(self, content):
        """
        向所有已配置的机器人发送消息
        """
        results = {}
        for notifier in self.notifiers:
            try:
                success = notifier.send_message(content)
                results[notifier.bot_name] = success
            except Exception as e:
                print(f"发送到{notifier.bot_name}机器人时发生异常: {e}")
                results[notifier.bot_name] = False
        
        return results
    
    def get_notifier_count(self):
        """
        获取已配置的机器人推送器数量
        """
        return len(self.notifiers)


def test_bot_system():
    """
    测试机器人推送系统
    """
    print("=== 机器人推送系统测试 ===\n")
    
    # 测试数据
    test_content = """【今日实时票房同步】
实时大盘约：12345.67万
---
①电影A(1234万)-25.5%
②电影B(987万)-20.3%
③电影C(654万)-15.2%
---
同步时间：2024-01-01 12:00:00

<font color="info">[点击查看实时票房看板](https://piaofang.maoyan.com)</font>"""
    
    # 创建推送管理器
    manager = BotNotificationManager()
    
    # 添加企业微信机器人（使用测试URL）
    wecom_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=test-key"
    wecom_notifier = WeComBotNotifier(wecom_url)
    manager.add_notifier(wecom_notifier)
    print("✅ 已添加企业微信机器人")
    
    # 添加钉钉机器人（使用测试URL）
    dingtalk_url = "https://oapi.dingtalk.com/robot/send?access_token=test-token"
    dingtalk_notifier = DingTalkBotNotifier(dingtalk_url)
    manager.add_notifier(dingtalk_notifier)
    print("✅ 已添加钉钉机器人")
    
    print(f"\n配置的机器人数量: {manager.get_notifier_count()}")
    
    # 测试推送
    print("\n=== 开始测试推送 ===")
    results = manager.send_to_all(test_content)
    
    # 输出结果
    print("\n=== 推送结果 ===")
    success_count = sum(1 for success in results.values() if success)
    total_count = len(results)
    print(f"推送完成：{success_count}/{total_count} 个机器人推送成功")
    
    for bot_name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  - {bot_name}: {status}")
    
    print("\n=== 测试完成 ===")


def test_individual_notifiers():
    """
    测试单个推送器
    """
    print("\n=== 单个推送器测试 ===\n")
    
    test_message = "这是一条测试消息"
    
    # 测试企业微信推送器
    print("1. 测试企业微信推送器")
    wecom = WeComBotNotifier("https://test-wecom-url")
    wecom_result = wecom.send_message(test_message)
    print(f"   结果: {'成功' if wecom_result else '失败'}\n")
    
    # 测试钉钉推送器
    print("2. 测试钉钉推送器")
    dingtalk = DingTalkBotNotifier("https://test-dingtalk-url")
    dingtalk_result = dingtalk.send_message(test_message)
    print(f"   结果: {'成功' if dingtalk_result else '失败'}\n")
    
    # 测试空URL
    print("3. 测试空URL处理")
    empty_wecom = WeComBotNotifier("")
    empty_result = empty_wecom.send_message(test_message)
    print(f"   结果: {'成功' if empty_result else '失败'}\n")


if __name__ == "__main__":
    print("🤖 机器人推送系统测试脚本")
    print("=" * 50)
    
    # 运行测试
    test_individual_notifiers()
    test_bot_system()
    
    print("\n" + "=" * 50)
    print("✅ 所有测试完成！")
    print("\n💡 提示：")
    print("   - 此测试使用模拟请求，不会发送真实消息")
    print("   - 在生产环境中，请替换为真实的Webhook地址")
    print("   - 确保网络连接正常后再进行真实测试")
