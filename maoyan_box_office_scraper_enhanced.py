# -*- coding: utf-8 -*-
"""
猫眼票房数据爬虫 - 增强版（支持配置文件）

此版本展示如何使用外部配置文件来管理多个机器人推送
如果您希望使用配置文件方式，可以参考此文件的实现
"""

import requests
import json
import time
import os
import random
import math

# 尝试导入配置文件，如果不存在则使用默认配置
try:
    from bot_config import BOT_CONFIGS, PUSH_SETTINGS, MESSAGE_SETTINGS, get_enabled_bots
    USE_CONFIG_FILE = True
    print("✅ 已加载外部配置文件")
except ImportError:
    USE_CONFIG_FILE = False
    print("⚠️  未找到配置文件，使用默认配置")
    # 默认配置
    BOT_CONFIGS = {
        "wecom": {
            "enabled": True,
            "webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=09de6958-992c-453b-93ec-1c48d0c1a1fe"
        }
    }
    PUSH_SETTINGS = {"show_detailed_results": True, "timeout": 10}
    MESSAGE_SETTINGS = {"add_view_link": True, "link_url": "https://piaofang.maoyan.com"}
    
    def get_enabled_bots():
        return {name: config for name, config in BOT_CONFIGS.items() if config.get("enabled", False)}

# --- 在这里添加时间判断逻辑 ---
current_hour = time.localtime().tm_hour
# 小时的范围是 0-23。我们只在 10点 到 22点 之间运行。
if not (10 <= current_hour <= 22):
    print(f"当前时间 {current_hour}点，不在允许的执行时间内（10:00-22:59），脚本退出。")
    exit() # 直接退出程序
# -----------------------------

def get_box_office_data():
    """
    从猫眼票房API获取数据
    """
    # 基础URL
    base_url = 'https://piaofang.maoyan.com/getBoxList'
    
    # 生成当前时间戳和随机索引
    timestamp = str(math.ceil(time.time() * 1000))
    index = str(round(random.random() * 1000))
    
    # 这里的signKey是固定的，实际上可能需要根据其他参数动态生成
    # 但为了简化，我们先使用固定值测试
    sign_key = '7f498fbe'
    
    # 构建请求参数
    params = {
        'date': '1',
        'isSplit': 'true',
        'timeStamp': timestamp,
        'index': index,
        'channelId': '40009',
        'sVersion': '1',
        'signKey': sign_key
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
        'Referer': 'https://piaofang.maoyan.com/dashboard',
        'Accept': 'application/json, text/plain, */*'
    }

    try:
        response = requests.get(base_url, headers=headers, params=params, timeout=10)
        response.raise_for_status()
        
        # 解析JSON响应
        data = response.json()
        return data
            
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"解析JSON失败: {e}")
        print(f"响应内容: {response.text[:200]}...")  # 打印前200个字符以便调试
        return None

def process_and_format_data(data):
    """
    处理并格式化票房数据
    """
    if not data or 'boxOffice' not in data or 'data' not in data['boxOffice'] or 'list' not in data['boxOffice']['data']:
        print("未找到有效的数据或数据格式不正确。")
        return None

    movies = data['boxOffice']['data']['list']
    if not movies:
        print("电影列表为空")
        return None
    
    # 定义一个安全的转换函数，用于处理票房值
    def parse_box_office(box_value):
        if not box_value or box_value == 'N/A':
            return 0.0
        
        clean_value = str(box_value).replace(',', '')
        
        if '万' in clean_value:
            try:
                return float(clean_value.replace('万', ''))
            except ValueError:
                return 0.0
        elif '亿' in clean_value:
            try:
                return float(clean_value.replace('亿', '')) * 10000
            except ValueError:
                return 0.0
        else:
            try:
                return float(clean_value)  # 假设无单位的票房数值是"万"
            except ValueError:
                return 0.0

    # 计算总票房
    total_box_office = sum(parse_box_office(movie.get('boxDesc', '0')) for movie in movies)
    
    # 格式化总票房，统一单位为"万"
    formatted_total = f"{total_box_office:.2f}万"
    
    # 获取当前时间
    current_time = time.strftime("%Y-%m-%d %H:%M:%S")
    
    # 格式化输出头部
    formatted_output = f"【今日实时票房同步】\n实时大盘约：{formatted_total}\n---\n"
    
    # 定义一个安全的转换函数，用于处理百分比
    def safe_float_convert(value):
        if not value:
            return 0
        clean_value = value.replace('%', '').replace('<', '')
        try:
            return float(clean_value)
        except ValueError:
            return 0
    
    # 按照票房占比排序电影列表
    sorted_movies = sorted(movies, key=lambda x: safe_float_convert(x.get('boxRate', '0')), reverse=True)
    
    # 中文数字映射
    chinese_numbers = ['①', '②', '③', '④', '⑤', '⑥', '⑦', '⑧', '⑨', '⑩']
    
    # 生成每部电影的格式化字符串
    for i, movie in enumerate(sorted_movies[:10]):
        if i < len(chinese_numbers):
            number = chinese_numbers[i]
        else:
            number = f"{i+1}."
            
        movie_name = movie.get('movieInfo', {}).get('movieName', 'N/A')
        box_office_str = movie.get('boxDesc', 'N/A')
        
        # 确保票房有"万"单位
        if box_office_str and '万' not in box_office_str and '亿' not in box_office_str:
            # 检查是否为纯数字或者可转换为数字的字符串
            try:
                float(box_office_str)
                box_office_str = f"{box_office_str}万"
            except (ValueError, TypeError):
                pass # 保持原样

        # 使用票房占比代替排片率
        box_rate = movie.get('boxRate', 'N/A')
        
        formatted_output += f"{number}{movie_name}({box_office_str})-{box_rate}\n"
    
    # 添加结尾时间信息
    formatted_output += f"---\n同步时间：{current_time}"
    
    return formatted_output

# 导入原有的机器人推送系统
from maoyan_box_office_scraper import (
    BotNotifier, WeComBotNotifier, DingTalkBotNotifier, BotNotificationManager
)

def create_notification_manager_from_config():
    """
    根据配置文件创建推送管理器
    
    返回:
    BotNotificationManager: 配置好的推送管理器
    """
    manager = BotNotificationManager()
    enabled_bots = get_enabled_bots()
    
    for bot_name, config in enabled_bots.items():
        webhook_url = config.get("webhook_url")
        if not webhook_url:
            continue
            
        try:
            if bot_name == "wecom":
                notifier = WeComBotNotifier(webhook_url)
                manager.add_notifier(notifier)
                print(f"✅ 已添加企业微信机器人推送")
            elif bot_name == "dingtalk":
                notifier = DingTalkBotNotifier(webhook_url)
                manager.add_notifier(notifier)
                print(f"✅ 已添加钉钉机器人推送")
            # 未来可以在这里添加更多机器人类型
            else:
                print(f"⚠️  暂不支持 {bot_name} 类型的机器人")
        except Exception as e:
            print(f"❌ 添加 {bot_name} 机器人失败: {e}")
    
    return manager

if __name__ == '__main__':
    print("开始爬取猫眼电影票房数据...")
    raw_data = get_box_office_data()
    
    if raw_data:
        # 格式化并显示数据
        formatted_data = process_and_format_data(raw_data)
        if formatted_data:
            print("\n" + formatted_data + "\n")

            # 构建推送内容
            report_content = formatted_data
            if MESSAGE_SETTINGS.get("add_view_link", True):
                link_text = MESSAGE_SETTINGS.get("link_text", "点击查看实时票房看板")
                link_url = MESSAGE_SETTINGS.get("link_url", "https://piaofang.maoyan.com")
                report_content += f'\n\n<font color="info">[{link_text}]({link_url})</font>'
            
            # 创建并配置推送管理器
            notification_manager = create_notification_manager_from_config()
            
            # 发送消息到所有配置的机器人
            if notification_manager.get_notifier_count() > 0:
                print(f"准备向 {notification_manager.get_notifier_count()} 个机器人推送消息...")
                results = notification_manager.send_to_all(report_content)
                
                # 输出推送结果摘要
                success_count = sum(1 for success in results.values() if success)
                total_count = len(results)
                print(f"推送完成：{success_count}/{total_count} 个机器人推送成功")
                
                # 详细结果（根据配置决定是否显示）
                if PUSH_SETTINGS.get("show_detailed_results", True):
                    for bot_name, success in results.items():
                        status = "成功" if success else "失败"
                        print(f"  - {bot_name}: {status}")
            else:
                print("未配置任何机器人推送，跳过推送步骤。")
                if USE_CONFIG_FILE:
                    print("提示：请检查 bot_config.py 中的机器人配置。")
                else:
                    print("提示：请创建 bot_config.py 配置文件或使用原版脚本。")
        else:
            print("格式化数据失败。")
    else:
        print("获取数据失败，请检查网络连接。")
    
    print("所有任务完成。")
