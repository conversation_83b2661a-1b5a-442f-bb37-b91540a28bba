# 猫眼票房数据爬虫

一个用于爬取猫眼电影票房数据并推送到多种机器人平台的Python脚本。

## 功能特性

- 🎬 **实时票房数据**：从猫眼票房API获取最新的电影票房数据
- 📊 **数据处理**：自动计算总票房，按占比排序电影列表
- 🤖 **多平台推送**：支持企业微信、钉钉等多种机器人推送
- ⏰ **时间控制**：仅在工作时间（10:00-22:59）运行
- 🔧 **可扩展架构**：易于添加新的机器人推送平台

## 文件说明

### 核心文件

- `maoyan_box_office_scraper.py` - 主程序文件（已重构支持多机器人）
- `requirements.txt` - Python依赖包列表

### 配置文件（可选）

- `bot_config_example.py` - 机器人配置文件示例
- `maoyan_box_office_scraper_enhanced.py` - 使用配置文件的增强版示例

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 基础使用（直接修改代码）

编辑 `maoyan_box_office_scraper.py` 文件中的机器人配置：

```python
# 企业微信机器人Webhook地址
WECOM_WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=你的密钥"

# 钉钉机器人Webhook地址（可选）
DINGTALK_WEBHOOK_URL = "https://oapi.dingtalk.com/robot/send?access_token=你的密钥"
```

然后运行：

```bash
python maoyan_box_office_scraper.py
```

### 3. 高级使用（配置文件方式）

1. 复制配置文件模板：
```bash
cp bot_config_example.py bot_config.py
```

2. 编辑 `bot_config.py`，填入您的机器人配置：
```python
BOT_CONFIGS = {
    "wecom": {
        "enabled": True,
        "webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=你的密钥"
    },
    "dingtalk": {
        "enabled": True,
        "webhook_url": "https://oapi.dingtalk.com/robot/send?access_token=你的密钥"
    }
}
```

3. 运行增强版脚本：
```bash
python maoyan_box_office_scraper_enhanced.py
```

## 支持的机器人平台

### 当前支持

- ✅ **企业微信机器人**
  - 支持Markdown格式消息
  - 频率限制：20条/分钟
  - 消息大小：4096字节

- ✅ **钉钉机器人**
  - 支持Markdown格式消息
  - 频率限制：20条/分钟
  - 消息大小：20000字节

### 预留扩展

- 🔄 **飞书机器人**（架构已预留）
- 🔄 **Slack机器人**（架构已预留）
- 🔄 **自定义Webhook**（架构已预留）

## 架构设计

### 类层次结构

```
BotNotifier (基类)
├── WeComBotNotifier (企业微信)
├── DingTalkBotNotifier (钉钉)
└── [未来扩展的机器人类]

BotNotificationManager (推送管理器)
└── 管理多个 BotNotifier 实例
```

### 核心组件

1. **数据爬取层**：`get_box_office_data()` - 负责从API获取数据
2. **数据处理层**：`process_and_format_data()` - 负责数据清洗和格式化
3. **推送抽象层**：`BotNotifier` - 定义统一的推送接口
4. **推送实现层**：各种具体的机器人推送器
5. **推送管理层**：`BotNotificationManager` - 统一管理多个推送器

## 添加新的机器人平台

### 1. 创建新的推送器类

```python
class NewBotNotifier(BotNotifier):
    def __init__(self, webhook_url):
        super().__init__(webhook_url, "新机器人")
    
    def send_message(self, content):
        # 实现具体的发送逻辑
        data = {
            "msgtype": "markdown",
            "markdown": {"content": content}
        }
        
        success, result = self._make_request(data)
        if success and result:
            # 处理响应结果
            return result.get('errcode') == 0
        return False
```

### 2. 在主程序中添加支持

```python
# 在配置区域添加新机器人的URL
NEW_BOT_WEBHOOK_URL = "https://api.newbot.com/webhook/..."

# 在推送逻辑中添加新机器人
if NEW_BOT_WEBHOOK_URL:
    new_notifier = NewBotNotifier(NEW_BOT_WEBHOOK_URL)
    notification_manager.add_notifier(new_notifier)
```

## 配置说明

### 环境变量支持

为了安全起见，建议使用环境变量管理敏感信息：

```python
import os

WECOM_WEBHOOK_URL = os.getenv('WECOM_WEBHOOK_URL', '')
DINGTALK_WEBHOOK_URL = os.getenv('DINGTALK_WEBHOOK_URL', '')
```

### 配置文件结构

```python
BOT_CONFIGS = {
    "机器人名称": {
        "enabled": True/False,      # 是否启用
        "webhook_url": "URL地址",   # Webhook地址
        "description": "描述信息"   # 可选的描述
    }
}
```

## 注意事项

1. **密钥安全**：请勿将真实的机器人密钥提交到版本控制系统
2. **频率限制**：注意各平台的API调用频率限制
3. **消息大小**：注意各平台的消息大小限制
4. **网络超时**：脚本设置了10秒的网络超时时间
5. **时间限制**：脚本仅在10:00-22:59之间运行

## 故障排除

### 常见问题

1. **推送失败**
   - 检查Webhook地址是否正确
   - 检查网络连接
   - 检查机器人是否被禁用

2. **数据获取失败**
   - 检查网络连接
   - 检查API是否变更
   - 检查请求参数是否正确

3. **配置文件错误**
   - 检查配置文件语法
   - 检查文件路径
   - 检查导入语句

### 调试模式

在配置中启用详细结果显示：

```python
PUSH_SETTINGS = {
    "show_detailed_results": True
}
```

## 许可证

本项目仅供学习和研究使用，请遵守相关网站的使用条款。

## 更新日志

- **v2.0** - 重构推送系统，支持多平台机器人
- **v1.0** - 基础版本，仅支持企业微信机器人
